<template>
  <div class="modal fade" id="procedimentoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-gradient-light">
          <h5 class="modal-title text-dark">
            <i class="fas fa-user-md me-2"></i>
            {{ isEditing ? 'Editar Procedimento' : 'Novo Procedimento' }}
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>

        <form @submit.prevent="salvar">
          <div class="modal-body py-3">
            <div class="row g-2">
              <!-- Linha 1: Nome e Código -->
              <div class="col-md-8">
                <label class="form-label">Nome *</label>
                <input
                  type="text"
                  class="form-control form-control-sm"
                  v-model="form.nome"
                  :class="{ 'is-invalid': errors.nome }"
                  placeholder="Nome do procedimento"
                  maxlength="255"
                  required
                >
                <div v-if="errors.nome" class="invalid-feedback">{{ errors.nome }}</div>
              </div>

              <div class="col-md-4">
                <label class="form-label">Código <small class="text-muted">(opcional)</small></label>
                <input
                  type="text"
                  class="form-control form-control-sm"
                  v-model="form.codigo"
                  :class="{ 'is-invalid': errors.codigo }"
                  placeholder="Ex: PROC001"
                  maxlength="50"
                >
                <div v-if="errors.codigo" class="invalid-feedback">{{ errors.codigo }}</div>
              </div>

              <!-- Linha 2: Categoria e Tipo -->
              <div class="col-md-6">
                <label class="form-label">Categoria</label>
                <select
                  class="form-select form-select-sm"
                  v-model="form.categoria_id"
                  :class="{ 'is-invalid': errors.categoria_id }"
                >
                  <option value="">Selecione uma categoria</option>
                  <option
                    v-for="categoria in categorias"
                    :key="categoria.id"
                    :value="categoria.id"
                  >
                    {{ categoria.nome }}
                  </option>
                </select>
                <div v-if="errors.categoria_id" class="invalid-feedback">{{ errors.categoria_id }}</div>
              </div>

              <div class="col-md-6">
                <label class="form-label">Tipo *</label>
                <select
                  class="form-select form-select-sm"
                  v-model="form.tipo"
                  :class="{ 'is-invalid': errors.tipo }"
                  required
                >
                  <option value="procedimento">Procedimento</option>
                  <option value="servico">Serviço</option>
                  <option value="produto">Produto</option>
                </select>
                <div v-if="errors.tipo" class="invalid-feedback">{{ errors.tipo }}</div>
              </div>

              <!-- Linha 3: Valores -->
              <div class="col-md-4">
                <label class="form-label">Valor Base *</label>
                <div class="input-group input-group-sm">
                  <span class="input-group-text">R$</span>
                  <input
                    type="number"
                    class="form-control"
                    v-model.number="form.valor_base"
                    :class="{ 'is-invalid': errors.valor_base }"
                    placeholder="0,00"
                    step="0.01"
                    min="0.01"
                    required
                  >
                </div>
                <div v-if="errors.valor_base" class="invalid-feedback">{{ errors.valor_base }}</div>
              </div>

              <div class="col-md-4">
                <label class="form-label">Valor Mín. <small class="text-muted">(opcional)</small></label>
                <div class="input-group input-group-sm">
                  <span class="input-group-text">R$</span>
                  <input
                    type="number"
                    class="form-control"
                    v-model.number="form.valor_minimo"
                    :class="{ 'is-invalid': errors.valor_minimo }"
                    placeholder="0,00"
                    step="0.01"
                    min="0"
                  >
                </div>
                <div v-if="errors.valor_minimo" class="invalid-feedback">{{ errors.valor_minimo }}</div>
              </div>

              <div class="col-md-4">
                <label class="form-label">Valor Máx. <small class="text-muted">(opcional)</small></label>
                <div class="input-group input-group-sm">
                  <span class="input-group-text">R$</span>
                  <input
                    type="number"
                    class="form-control"
                    v-model.number="form.valor_maximo"
                    :class="{ 'is-invalid': errors.valor_maximo }"
                    placeholder="0,00"
                    step="0.01"
                    min="0"
                  >
                </div>
                <div v-if="errors.valor_maximo" class="invalid-feedback">{{ errors.valor_maximo }}</div>
              </div>

              <!-- Linha 4: Unidade e Tempo -->
              <div class="col-md-6">
                <label class="form-label">Unidade</label>
                <select
                  class="form-select form-select-sm"
                  v-model="form.unidade"
                  :class="{ 'is-invalid': errors.unidade }"
                >
                  <option value="un">Unidade</option>
                  <option value="h">Hora</option>
                  <option value="min">Minuto</option>
                  <option value="sessao">Sessão</option>
                  <option value="consulta">Consulta</option>
                  <option value="kg">Quilograma</option>
                  <option value="g">Grama</option>
                  <option value="ml">Mililitro</option>
                  <option value="l">Litro</option>
                </select>
                <div v-if="errors.unidade" class="invalid-feedback">{{ errors.unidade }}</div>
              </div>

              <div class="col-md-6">
                <label class="form-label">Tempo Estimado <small class="text-muted">(minutos)</small></label>
                <input
                  type="number"
                  class="form-control form-control-sm"
                  v-model.number="form.tempo_estimado"
                  :class="{ 'is-invalid': errors.tempo_estimado }"
                  placeholder="Ex: 60"
                  min="1"
                >
                <div v-if="errors.tempo_estimado" class="invalid-feedback">{{ errors.tempo_estimado }}</div>
              </div>

              <!-- Linha 5: Descrição e Observações (compactas) -->
              <div class="col-md-6">
                <label class="form-label">Descrição</label>
                <textarea
                  class="form-control form-control-sm"
                  v-model="form.descricao"
                  :class="{ 'is-invalid': errors.descricao }"
                  placeholder="Descrição do procedimento"
                  rows="2"
                ></textarea>
                <div v-if="errors.descricao" class="invalid-feedback">{{ errors.descricao }}</div>
              </div>

              <div class="col-md-6">
                <label class="form-label">Observações</label>
                <textarea
                  class="form-control form-control-sm"
                  v-model="form.observacoes"
                  :class="{ 'is-invalid': errors.observacoes }"
                  placeholder="Observações adicionais"
                  rows="2"
                ></textarea>
                <div v-if="errors.observacoes" class="invalid-feedback">{{ errors.observacoes }}</div>
              </div>
            </div>
          </div>

          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
              <i class="fas fa-times me-1"></i>
              Cancelar
            </button>
            <button type="submit" class="btn btn-primary" :disabled="loading">
              <span v-if="loading" class="spinner-border spinner-border-sm me-2"></span>
              <i v-else class="fas fa-save me-1"></i>
              {{ loading ? 'Salvando...' : 'Salvar' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import { servicoProdutoService } from '@/services/servicoProdutoService';
import { openModal, closeModal } from '@/utils/modalHelper';
import cSwal from '@/utils/cSwal';

export default {
  name: 'ProcedimentoModal',
  props: {
    categorias: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      isEditing: false,
      loading: false,
      procedimentoId: null,
      form: {
        categoria_id: '',
        codigo: '',
        nome: '',
        descricao: '',
        tipo: 'procedimento',
        valor_base: 0,
        valor_minimo: null,
        valor_maximo: null,
        unidade: 'un',
        tempo_estimado: null,
        observacoes: ''
      },
      errors: {}
    };
  },
  methods: {
    open(procedimento = null) {
      this.resetForm();
      this.errors = {};
      
      if (procedimento) {
        this.isEditing = true;
        this.procedimentoId = procedimento.id;
        this.form = {
          categoria_id: procedimento.categoria_id || '',
          codigo: procedimento.codigo || '',
          nome: procedimento.nome || '',
          descricao: procedimento.descricao || '',
          tipo: procedimento.tipo || 'procedimento',
          valor_base: procedimento.valor_base || 0,
          valor_minimo: procedimento.valor_minimo || null,
          valor_maximo: procedimento.valor_maximo || null,
          unidade: procedimento.unidade || 'un',
          tempo_estimado: procedimento.tempo_estimado || null,
          observacoes: procedimento.observacoes || ''
        };
      } else {
        this.isEditing = false;
        this.procedimentoId = null;
      }

      openModal('procedimentoModal');
    },

    resetForm() {
      this.form = {
        categoria_id: '',
        codigo: '',
        nome: '',
        descricao: '',
        tipo: 'procedimento',
        valor_base: 0,
        valor_minimo: null,
        valor_maximo: null,
        unidade: 'un',
        tempo_estimado: null,
        observacoes: ''
      };
    },

    validateForm() {
      this.errors = {};
      let isValid = true;

      if (!this.form.nome || this.form.nome.trim() === '') {
        this.errors.nome = 'O nome é obrigatório';
        isValid = false;
      }

      if (!this.form.tipo) {
        this.errors.tipo = 'O tipo é obrigatório';
        isValid = false;
      }

      if (!this.form.valor_base || this.form.valor_base <= 0) {
        this.errors.valor_base = 'O valor base deve ser maior que zero';
        isValid = false;
      }

      if (this.form.valor_minimo && this.form.valor_maximo) {
        if (this.form.valor_minimo > this.form.valor_maximo) {
          this.errors.valor_minimo = 'O valor mínimo não pode ser maior que o máximo';
          isValid = false;
        }
      }

      if (this.form.valor_minimo && this.form.valor_minimo > this.form.valor_base) {
        this.errors.valor_minimo = 'O valor mínimo não pode ser maior que o valor base';
        isValid = false;
      }

      if (this.form.valor_maximo && this.form.valor_maximo < this.form.valor_base) {
        this.errors.valor_maximo = 'O valor máximo não pode ser menor que o valor base';
        isValid = false;
      }

      return isValid;
    },

    async salvar() {
      if (!this.validateForm()) {
        return;
      }

      this.loading = true;
      this.errors = {};

      try {
        const data = { ...this.form };
        
        // Limpar valores nulos/vazios
        if (!data.categoria_id) data.categoria_id = null;
        if (!data.codigo) data.codigo = null;
        if (!data.valor_minimo) data.valor_minimo = null;
        if (!data.valor_maximo) data.valor_maximo = null;
        if (!data.tempo_estimado) data.tempo_estimado = null;

        let response;
        if (this.isEditing) {
          response = await servicoProdutoService.updateServicoProduto(this.procedimentoId, data);
        } else {
          response = await servicoProdutoService.createServicoProduto(data);
        }

        cSwal.cSuccess(
          this.isEditing ? 'Procedimento atualizado com sucesso!' : 'Procedimento criado com sucesso!'
        );

        closeModal('procedimentoModal');
        this.$emit('saved', response.data.data);

      } catch (error) {
        console.error('Erro ao salvar procedimento:', error);
        
        if (error.response && error.response.data && error.response.data.data) {
          this.errors = error.response.data.data;
        } else {
          cSwal.cError('Erro ao salvar procedimento');
        }
      } finally {
        this.loading = false;
      }
    }
  }
};
</script>

<style scoped>
.modal-header {
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.bg-gradient-light {
  background: linear-gradient(87deg, #f8f9fc 0, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.4rem;
  font-size: 0.875rem;
}

.form-control, .form-select {
  border-radius: 0.375rem;
  border: 1px solid #d1d3e2;
  transition: all 0.15s ease-in-out;
}

.form-control-sm, .form-select-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.form-control:focus, .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.input-group-text {
  background-color: #f8f9fc;
  border-color: #d1d3e2;
  color: #5a5c69;
  font-size: 0.875rem;
}

.input-group-sm .input-group-text {
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
}

.btn-primary {
  background: linear-gradient(87deg, #007bff 0, #0056b3 100%);
  border-color: #007bff;
}

.btn-primary:hover {
  background: linear-gradient(87deg, #0056b3 0, #004085 100%);
  border-color: #0056b3;
}
</style>
